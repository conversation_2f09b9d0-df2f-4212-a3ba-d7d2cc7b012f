"""
Vérification des utilisateurs dans la base MySQL
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.user import User

def check_users():
    """Vérifie les utilisateurs dans la base de données"""
    print("👤 Vérification des utilisateurs dans MySQL...")
    
    # URL de connexion MySQL
    database_url = "mysql+pymysql://root:@localhost:3306/archivage_db"
    
    try:
        # Créer le moteur et la session
        engine = create_engine(database_url)
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        # Récupérer tous les utilisateurs
        users = db.query(User).all()
        
        print(f"\n📊 {len(users)} utilisateur(s) trouvé(s) :")
        print("-" * 60)
        
        for user in users:
            print(f"ID: {user.id}")
            print(f"Username: '{user.username}'")
            print(f"Email: {user.email}")
            print(f"Nom: {user.first_name} {user.last_name}")
            print(f"Rôle: {user.role}")
            print(f"Actif: {user.is_active}")
            print(f"Créé: {user.created_at}")
            print("-" * 60)
        
        db.close()
        
        if users:
            print("\n✅ Utilisateurs disponibles pour la connexion :")
            for user in users:
                print(f"   - Username: '{user.username}' | Email: {user.email}")
        else:
            print("\n❌ Aucun utilisateur trouvé dans la base")
            
        return users
        
    except Exception as e:
        print(f"❌ Erreur de connexion à la base : {e}")
        return []

def test_login_credentials():
    """Test les identifiants de connexion"""
    print("\n🔐 Test des identifiants de connexion...")
    
    from app.core.security import verify_password
    
    database_url = "mysql+pymysql://root:@localhost:3306/archivage_db"
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        # Test admin
        admin = db.query(User).filter(User.username == "admin").first()
        if admin:
            print(f"✅ Utilisateur 'admin' trouvé")
            # Test du mot de passe
            if verify_password("admin123", admin.hashed_password):
                print(f"✅ Mot de passe 'admin123' correct")
            else:
                print(f"❌ Mot de passe 'admin123' incorrect")
        else:
            print(f"❌ Utilisateur 'admin' non trouvé")
        
        # Test testuser
        testuser = db.query(User).filter(User.username == "testuser").first()
        if testuser:
            print(f"✅ Utilisateur 'testuser' trouvé")
            if verify_password("test123", testuser.hashed_password):
                print(f"✅ Mot de passe 'test123' correct")
            else:
                print(f"❌ Mot de passe 'test123' incorrect")
        else:
            print(f"❌ Utilisateur 'testuser' non trouvé")
            
    except Exception as e:
        print(f"❌ Erreur lors du test : {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🔍 Vérification des utilisateurs MySQL")
    print("=" * 50)
    
    users = check_users()
    
    if users:
        test_login_credentials()
        
        print("\n📋 Résumé pour la connexion :")
        print("   Username: admin")
        print("   Password: admin123")
        print("   ⚠️  Attention aux fautes de frappe !")
    else:
        print("\n❌ Aucun utilisateur trouvé. Relancez l'initialisation.")
