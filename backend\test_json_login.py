"""
Test du nouveau endpoint login JSON
"""

import requests
import json

def test_json_login():
    """Test du login avec JSON"""
    print("🔐 Test du nouveau endpoint login JSON...")
    
    base_url = "http://localhost:8000"
    
    # Test avec JSON
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/auth/login-json",
            json=login_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ Login JSON réussi !")
            print(f"Token: {token[:50]}...")
            
            # Test du profil
            headers = {"Authorization": f"Bearer {token}"}
            me_response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=10)
            
            print(f"Me endpoint status: {me_response.status_code}")
            if me_response.status_code == 200:
                profile = me_response.json()
                print(f"✅ Profil: {profile.get('username')} ({profile.get('email')})")
                return True
            else:
                print(f"❌ Erreur profil: {me_response.text}")
                return False
        else:
            print(f"❌ Erreur login: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test endpoint login JSON")
    print("=" * 40)
    
    if test_json_login():
        print("\n✅ Le nouveau endpoint fonctionne !")
        print("Le frontend devrait maintenant pouvoir se connecter.")
    else:
        print("\n❌ Problème avec le nouveau endpoint")
