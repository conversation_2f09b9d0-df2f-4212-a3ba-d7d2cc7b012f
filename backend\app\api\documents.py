"""
Routes pour la gestion des documents
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form, BackgroundTasks
from sqlalchemy.orm import Session
from ..core.database import get_db
from ..core.security import get_current_active_user
from ..crud import document as crud_document
from ..schemas.document import Document, DocumentCreate, DocumentUpdate
from ..schemas.tag import Tag
from ..schemas.category import Category
from ..models.user import User
from ..services.file_service import file_service
from ..services.ocr_service import ocr_service

router = APIRouter()

def enrich_document_dict(document, current_user=None):
    """Enrichit un document et retourne un dictionnaire"""
    doc_dict = {
        "id": document.id,
        "title": document.title,
        "description": document.description,
        "filename": document.filename,
        "original_filename": document.original_filename,
        "file_path": document.file_path,
        "file_size": document.file_size,
        "mime_type": document.mime_type,
        "ocr_text": document.ocr_text,
        "ocr_confidence": document.ocr_confidence,
        "category_id": document.category_id,
        "owner_id": document.owner_id,
        "created_at": document.created_at,
        "updated_at": document.updated_at,
        "file_url": file_service.get_file_url(document.file_path),
        "file_size_mb": round(document.file_size / (1024 * 1024), 2) if document.file_size else 0,
        "tags": [],
        "category": None
    }

    if current_user:
        doc_dict["owner"] = {
            "id": current_user.id,
            "username": current_user.username,
            "first_name": current_user.first_name,
            "last_name": current_user.last_name
        }
    else:
        doc_dict["owner"] = None

    return doc_dict

async def process_ocr_background(document_id: int, file_path: str, mime_type: str, db: Session):
    """Traitement OCR en arrière-plan"""
    try:
        # Extraire le texte OCR
        ocr_text, ocr_confidence = ocr_service.extract_text_from_file(file_path, mime_type)
        
        # Mettre à jour le document avec le contenu OCR
        crud_document.update_document_ocr(db, document_id, ocr_text, ocr_confidence)
        
        # Suggérer des tags basés sur le contenu OCR
        if ocr_text:
            suggested_tags = ocr_service.suggest_tags_from_text(ocr_text)
            if suggested_tags:
                crud_document.add_tags_to_document(db, document_id, suggested_tags)
        
    except Exception as e:
        print(f"Erreur lors du traitement OCR: {e}")

@router.post("/upload")
async def upload_document(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    title: str = Form(...),
    description: Optional[str] = Form(None),
    category_id: Optional[int] = Form(None),
    tag_names: Optional[str] = Form(None),  # Tags séparés par des virgules
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Upload d'un nouveau document"""
    
    # Sauvegarder le fichier
    file_path, filename, file_size = await file_service.save_file(file)
    mime_type = file_service.get_mime_type(file.filename)
    
    # Préparer les tags
    tags = []
    if tag_names:
        tags = [tag.strip() for tag in tag_names.split(",") if tag.strip()]
    
    # Créer l'objet document
    document_data = DocumentCreate(
        title=title,
        description=description,
        category_id=category_id,
        tag_names=tags
    )
    
    try:
        # Créer le document en base
        document = crud_document.create_document(
            db=db,
            document=document_data,
            filename=filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file_size,
            mime_type=mime_type,
            owner_id=current_user.id
        )
        
        # Lancer le traitement OCR en arrière-plan
        background_tasks.add_task(
            process_ocr_background,
            document.id,
            file_path,
            mime_type,
            db
        )

        # Enrichir le document avec les champs calculés
        return enrich_document_dict(document, current_user)
        
    except Exception as e:
        # Nettoyer le fichier en cas d'erreur
        file_service.delete_file(file_path)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la création du document: {str(e)}"
        )

@router.get("/", response_model=List[Document])
async def read_documents(
    skip: int = 0,
    limit: int = 100,
    category_id: Optional[int] = None,
    search: Optional[str] = None,
    my_documents: bool = False,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Récupère la liste des documents"""
    
    owner_id = current_user.id if my_documents else None
    
    # Les utilisateurs non-admin ne peuvent voir que leurs propres documents
    from ..models.user import UserRole
    if current_user.role != UserRole.ADMIN and not my_documents:
        owner_id = current_user.id
    
    documents = crud_document.get_documents(
        db=db,
        skip=skip,
        limit=limit,
        owner_id=owner_id,
        category_id=category_id,
        search=search
    )
    
    # Ajouter les champs calculés à chaque document
    for doc in documents:
        doc.file_url = file_service.get_file_url(doc.file_path)
        doc.file_size_mb = round(doc.file_size / (1024 * 1024), 2) if doc.file_size else 0

    return documents

@router.get("/{document_id}", response_model=Document)
async def read_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Récupère un document par ID"""
    
    document = crud_document.get_document(db, document_id)
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document non trouvé"
        )
    
    # Vérifier les permissions
    if current_user.role != UserRole.ADMIN and document.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Pas assez de permissions"
        )
    
    # Ajouter les champs calculés
    document.file_url = file_service.get_file_url(document.file_path)
    document.file_size_mb = round(document.file_size / (1024 * 1024), 2) if document.file_size else 0

    return document

@router.put("/{document_id}", response_model=Document)
async def update_document(
    document_id: int,
    document_update: DocumentUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Met à jour un document"""
    
    document = crud_document.get_document(db, document_id)
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document non trouvé"
        )
    
    # Vérifier les permissions
    if current_user.role != UserRole.ADMIN and document.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Pas assez de permissions"
        )
    
    updated_document = crud_document.update_document(db, document_id, document_update)
    
    # Ajouter les champs calculés
    updated_document.file_url = file_service.get_file_url(updated_document.file_path)
    updated_document.file_size_mb = round(updated_document.file_size / (1024 * 1024), 2) if updated_document.file_size else 0

    return updated_document

@router.delete("/{document_id}")
async def delete_document(
    document_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Supprime un document"""
    
    document = crud_document.get_document(db, document_id)
    if not document:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Document non trouvé"
        )
    
    # Vérifier les permissions
    if current_user.role != UserRole.ADMIN and document.owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Pas assez de permissions"
        )
    
    # Supprimer le fichier physique
    file_service.delete_file(document.file_path)
    
    # Supprimer le document de la base
    success = crud_document.delete_document(db, document_id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Erreur lors de la suppression du document"
        )
    
    return {"message": "Document supprimé avec succès"}

@router.get("/categories/", response_model=List[Category])
async def read_categories(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Récupère toutes les catégories"""
    return crud_document.get_categories(db)

@router.get("/tags/", response_model=List[Tag])
async def read_tags(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Récupère tous les tags"""
    return crud_document.get_tags(db)
