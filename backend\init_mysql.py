"""
Initialisation MySQL simple et rapide
"""

from sqlalchemy import create_engine
from sqlalchemy.orm import Session
from app.models import Base, User, Category
from app.core.security import get_password_hash
from app.models.user import UserRole

def init_mysql_database():
    """Initialise la base de données MySQL"""
    print("🗄️ Initialisation de la base de données MySQL...")
    
    # URL de connexion MySQL (XAMPP par défaut)
    database_url = "mysql+pymysql://root:@localhost:3306/archivage_db"
    
    try:
        # Créer le moteur
        engine = create_engine(database_url, echo=True)
        
        # Créer toutes les tables
        print("📋 Création des tables...")
        Base.metadata.create_all(bind=engine)
        print("✅ Tables créées")
        
        # Créer une session
        from sqlalchemy.orm import sessionmaker
        SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
        db = SessionLocal()
        
        try:
            # Vérifier si des utilisateurs existent déjà
            existing_users = db.query(User).count()
            if existing_users > 0:
                print(f"ℹ️ {existing_users} utilisateur(s) déjà présent(s)")
                return True
            
            # Créer l'utilisateur admin
            print("👤 Création de l'utilisateur admin...")
            admin_user = User(
                email="<EMAIL>",
                username="admin",
                first_name="Admin",
                last_name="Système",
                hashed_password=get_password_hash("admin123"),
                role=UserRole.ADMIN,
                is_active=True
            )
            db.add(admin_user)
            
            # Créer un utilisateur test
            print("👤 Création de l'utilisateur test...")
            test_user = User(
                email="<EMAIL>",
                username="testuser",
                first_name="Test",
                last_name="User",
                hashed_password=get_password_hash("test123"),
                role=UserRole.USER,
                is_active=True
            )
            db.add(test_user)
            
            # Créer des catégories par défaut
            print("📁 Création des catégories...")
            categories = [
                Category(name="Factures", description="Documents de facturation", color="#EF4444"),
                Category(name="Contrats", description="Contrats et accords", color="#3B82F6"),
                Category(name="Rapports", description="Rapports et analyses", color="#10B981"),
                Category(name="Correspondance", description="Lettres et emails", color="#F59E0B"),
                Category(name="Certificats", description="Certificats et diplômes", color="#8B5CF6"),
            ]
            
            for category in categories:
                db.add(category)
            
            # Sauvegarder
            db.commit()
            print("✅ Données initiales créées")
            
            # Vérification
            user_count = db.query(User).count()
            category_count = db.query(Category).count()
            print(f"📊 {user_count} utilisateurs, {category_count} catégories créés")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur lors de la création des données: {e}")
            db.rollback()
            return False
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Erreur de connexion MySQL: {e}")
        print("\n💡 Vérifiez que:")
        print("   - La base 'archivage_db' existe dans phpMyAdmin")
        print("   - MySQL est démarré dans XAMPP")
        return False

if __name__ == "__main__":
    print("🚀 Initialisation MySQL pour le système d'archivage")
    print("=" * 60)
    
    if init_mysql_database():
        print("\n🎉 Initialisation MySQL réussie !")
        print("\n📋 Informations:")
        print("   - Base de données: archivage_db")
        print("   - URL: mysql://root:@localhost:3306/archivage_db")
        print("\n👤 Comptes créés:")
        print("   - Admin: admin / admin123")
        print("   - User: testuser / test123")
        print("\n🌐 Vérifiez dans phpMyAdmin:")
        print("   - http://localhost/phpmyadmin")
        print("   - Base: archivage_db")
    else:
        print("\n❌ Échec de l'initialisation")
        print("Créez d'abord la base 'archivage_db' dans phpMyAdmin")
