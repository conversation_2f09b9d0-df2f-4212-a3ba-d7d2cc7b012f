"""
Test rapide MySQL avec timeout
"""

import socket
import sys

def test_mysql_port():
    """Test si le port MySQL est ouvert"""
    print("🔌 Test du port MySQL...")
    
    ports_to_test = [3306, 3307, 3308]  # Ports MySQL courants
    
    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)  # Timeout de 2 secondes
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} est ouvert (MySQL probablement actif)")
                return port
            else:
                print(f"❌ Port {port} fermé")
        except Exception as e:
            print(f"❌ Erreur test port {port}: {e}")
    
    print("❌ Aucun port MySQL trouvé")
    return None

def test_apache_port():
    """Test si Apache est actif"""
    print("\n🌐 Test du port Apache...")
    
    ports_to_test = [80, 8080, 443]
    
    for port in ports_to_test:
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"✅ Port {port} ouvert (Apache probablement actif)")
                print(f"   Essayez: http://localhost:{port}/phpmyadmin")
                return port
            else:
                print(f"❌ Port {port} fermé")
        except Exception as e:
            print(f"❌ Erreur test port {port}: {e}")
    
    return None

def check_xampp_status():
    """Vérifications XAMPP"""
    print("\n📋 Vérifications XAMPP:")
    print("1. Ouvrez XAMPP Control Panel")
    print("2. Vérifiez que ces services sont 'Running' (verts):")
    print("   - Apache")
    print("   - MySQL")
    print("3. Si ils ne sont pas verts, cliquez 'Start'")
    print("4. Notez le port affiché pour MySQL (généralement 3306)")

if __name__ == "__main__":
    print("🚀 Diagnostic rapide XAMPP/MySQL")
    print("=" * 40)
    
    # Test des ports
    mysql_port = test_mysql_port()
    apache_port = test_apache_port()
    
    print(f"\n📊 Résultats:")
    if mysql_port:
        print(f"✅ MySQL détecté sur le port {mysql_port}")
    else:
        print("❌ MySQL non détecté")
    
    if apache_port:
        print(f"✅ Apache détecté sur le port {apache_port}")
        if apache_port == 80:
            print("   👉 Testez: http://localhost/phpmyadmin")
        else:
            print(f"   👉 Testez: http://localhost:{apache_port}/phpmyadmin")
    else:
        print("❌ Apache non détecté")
    
    check_xampp_status()
    
    if mysql_port and apache_port:
        print(f"\n🎉 XAMPP semble fonctionner !")
        print(f"   MySQL: port {mysql_port}")
        print(f"   Apache: port {apache_port}")
    else:
        print(f"\n⚠️ Problème détecté avec XAMPP")
        print(f"   Vérifiez XAMPP Control Panel")
