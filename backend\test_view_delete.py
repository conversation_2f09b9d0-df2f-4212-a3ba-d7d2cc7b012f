"""
Test de visualisation et suppression des documents
"""

import requests
import os

def test_view_and_delete():
    """Test complet de visualisation et suppression"""
    print("👁️ Test de visualisation et suppression...")
    
    base_url = "http://localhost:8000"
    
    # Étape 1: Se connecter
    print("\n1️⃣ Connexion...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Échec connexion: {login_response.status_code}")
            return False
        
        token = login_response.json().get("access_token")
        print(f"✅ Token obtenu: {token[:30]}...")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Étape 2: Récupérer la liste des documents
        print("\n2️⃣ Récupération des documents...")
        
        docs_response = requests.get(
            f"{base_url}/api/documents/",
            headers=headers,
            timeout=10
        )
        
        if docs_response.status_code != 200:
            print(f"❌ Erreur récupération documents: {docs_response.status_code}")
            return False
        
        documents = docs_response.json()
        print(f"✅ {len(documents)} documents trouvés")
        
        if not documents:
            print("ℹ️ Aucun document à tester")
            return True
        
        # Prendre le premier document
        document = documents[0]
        doc_id = document["id"]
        print(f"📄 Test avec document: {document['title']} (ID: {doc_id})")
        
        # Étape 3: Test de visualisation
        print("\n3️⃣ Test de visualisation...")
        
        view_response = requests.get(
            f"{base_url}/api/documents/{doc_id}/view",
            headers=headers,
            timeout=10
        )
        
        print(f"View Status: {view_response.status_code}")
        
        if view_response.status_code == 200:
            print(f"✅ Visualisation réussie")
            print(f"   Content-Type: {view_response.headers.get('content-type')}")
            print(f"   Content-Length: {len(view_response.content)} bytes")
        else:
            print(f"❌ Erreur visualisation: {view_response.text}")
        
        # Étape 4: Test de téléchargement
        print("\n4️⃣ Test de téléchargement...")
        
        download_response = requests.get(
            f"{base_url}/api/documents/{doc_id}/download",
            headers=headers,
            timeout=10
        )
        
        print(f"Download Status: {download_response.status_code}")
        
        if download_response.status_code == 200:
            print(f"✅ Téléchargement réussi")
            print(f"   Content-Type: {download_response.headers.get('content-type')}")
            print(f"   Content-Length: {len(download_response.content)} bytes")
        else:
            print(f"❌ Erreur téléchargement: {download_response.text}")
        
        # Étape 5: Test de l'URL statique
        print("\n5️⃣ Test de l'URL statique...")
        
        file_url = document.get("file_url")
        if file_url:
            static_response = requests.get(
                f"{base_url}{file_url}",
                timeout=10
            )
            
            print(f"Static URL Status: {static_response.status_code}")
            
            if static_response.status_code == 200:
                print(f"✅ URL statique accessible")
                print(f"   URL: {base_url}{file_url}")
            else:
                print(f"❌ URL statique inaccessible: {file_url}")
        else:
            print("❌ Pas d'URL de fichier dans le document")
        
        # Étape 6: Test de suppression (optionnel - décommentez si vous voulez tester)
        # print(f"\n6️⃣ Test de suppression...")
        # 
        # delete_response = requests.delete(
        #     f"{base_url}/api/documents/{doc_id}",
        #     headers=headers,
        #     timeout=10
        # )
        # 
        # print(f"Delete Status: {delete_response.status_code}")
        # 
        # if delete_response.status_code == 200:
        #     print(f"✅ Suppression réussie")
        #     result = delete_response.json()
        #     print(f"   Message: {result.get('message')}")
        # else:
        #     print(f"❌ Erreur suppression: {delete_response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_file_access():
    """Test d'accès direct aux fichiers"""
    print("\n📁 Test d'accès direct aux fichiers...")
    
    base_url = "http://localhost:8000"
    
    # Test d'accès à un fichier qui n'existe pas
    fake_url = f"{base_url}/uploads/fake/file.pdf"
    
    try:
        response = requests.get(fake_url, timeout=5)
        print(f"Fake file Status: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ Gestion correcte des fichiers inexistants")
        else:
            print("⚠️ Réponse inattendue pour fichier inexistant")
            
    except Exception as e:
        print(f"❌ Erreur test fichier: {e}")

if __name__ == "__main__":
    print("🚀 Test de visualisation et suppression")
    print("=" * 60)
    
    view_ok = test_view_and_delete()
    test_file_access()
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS:")
    print(f"   Visualisation/Téléchargement: {'✅' if view_ok else '❌'}")
    
    if view_ok:
        print("\n🎉 VISUALISATION ET TÉLÉCHARGEMENT FONCTIONNELS!")
        print("Les boutons 'Voir' et 'Télécharger' devraient maintenant marcher.")
        print("\n💡 Pour tester la suppression:")
        print("   - Décommentez la section 'Test de suppression' dans le code")
        print("   - Relancez le test")
    else:
        print("\n❌ Problèmes détectés")
        print("Vérifiez les logs du serveur backend.")
