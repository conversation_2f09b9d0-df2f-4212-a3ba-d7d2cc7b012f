"""
Configuration de l'application
"""

from pydantic_settings import BaseSettings
from typing import List
import os
from pathlib import Path

class Settings(BaseSettings):
    """Configuration de l'application avec validation Pydantic"""
    
    # Informations de l'application
    APP_NAME: str = "Système d'Archivage Électronique"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = True
    
    # Configuration de la base de données
    DATABASE_URL: str = "sqlite:///./archivage.db"
    DATABASE_HOST: str = "localhost"
    DATABASE_PORT: int = 3306
    DATABASE_NAME: str = "archivage_db"
    DATABASE_USER: str = "root"
    DATABASE_PASSWORD: str = "password"
    
    # Configuration JWT
    SECRET_KEY: str = "dev-secret-key-change-in-production"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # Configuration des uploads
    UPLOAD_DIR: str = "./uploads"
    MAX_FILE_SIZE: int = 10485760  # 10MB
    ALLOWED_EXTENSIONS: str = "pdf,jpg,jpeg,png"
    
    # Configuration OCR
    TESSERACT_PATH: str = "/usr/bin/tesseract"
    TESSERACT_LANG: str = "fra+eng"
    
    # Configuration CORS
    CORS_ORIGINS: List[str] = ["http://localhost:3000", "http://localhost:5173"]
    
    # Configuration de sécurité
    BCRYPT_ROUNDS: int = 12
    
    @property
    def allowed_extensions_list(self) -> List[str]:
        """Retourne la liste des extensions autorisées"""
        return [ext.strip().lower() for ext in self.ALLOWED_EXTENSIONS.split(",")]
    
    @property
    def database_url_sync(self) -> str:
        """URL de base de données synchrone pour SQLAlchemy"""
        if self.DATABASE_URL.startswith("mysql://"):
            return self.DATABASE_URL.replace("mysql://", "mysql+pymysql://")
        return self.DATABASE_URL
    
    class Config:
        env_file = ".env"
        case_sensitive = True

# Instance globale des paramètres
settings = Settings()
