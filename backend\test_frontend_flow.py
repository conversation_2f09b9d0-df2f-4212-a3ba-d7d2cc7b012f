"""
Test du flux complet frontend
"""

import requests
import time

def test_complete_frontend_flow():
    """Simule exactement le flux du frontend"""
    print("🌐 SIMULATION COMPLÈTE DU FLUX FRONTEND")
    print("=" * 60)
    
    base_url = "http://localhost:8000"
    
    # Étape 1: Login
    print("\n1️⃣ Connexion...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            headers={
                "Content-Type": "application/json",
                "Origin": "http://localhost:5173"
            },
            timeout=10
        )
        
        print(f"   Login Status: {login_response.status_code}")
        
        if login_response.status_code != 200:
            print(f"   ❌ Échec login: {login_response.text}")
            return False
        
        token_data = login_response.json()
        token = token_data.get("access_token")
        print(f"   ✅ Token obtenu: {token[:30]}...")
        
        # Étape 2: Récupération immédiate du profil (comme le frontend)
        print("\n2️⃣ Récupération du profil...")
        
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json",
            "Origin": "http://localhost:5173"
        }
        
        profile_response = requests.get(
            f"{base_url}/api/auth/me",
            headers=headers,
            timeout=10
        )
        
        print(f"   Profile Status: {profile_response.status_code}")
        
        if profile_response.status_code == 200:
            profile = profile_response.json()
            print(f"   ✅ Profil récupéré: {profile.get('username')}")
            print(f"   Email: {profile.get('email')}")
            print(f"   Role: {profile.get('role')}")
            
            # Étape 3: Test d'une requête protégée (documents)
            print("\n3️⃣ Test requête protégée (documents)...")
            
            docs_response = requests.get(
                f"{base_url}/api/documents/",
                headers=headers,
                timeout=10
            )
            
            print(f"   Documents Status: {docs_response.status_code}")
            
            if docs_response.status_code == 200:
                docs = docs_response.json()
                print(f"   ✅ Documents récupérés: {len(docs)} documents")
                return True
            else:
                print(f"   ❌ Erreur documents: {docs_response.text}")
                return False
                
        else:
            print(f"   ❌ Erreur profil: {profile_response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_multiple_requests():
    """Test de requêtes multiples avec le même token"""
    print("\n4️⃣ Test requêtes multiples...")
    
    base_url = "http://localhost:8000"
    
    # Login
    login_response = requests.post(
        f"{base_url}/api/auth/auth",
        json={"username": "admin", "password": "admin123"},
        timeout=10
    )
    
    if login_response.status_code != 200:
        print("   ❌ Échec login pour test multiple")
        return False
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Faire plusieurs requêtes rapidement
    for i in range(3):
        response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=5)
        print(f"   Requête {i+1}: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ Échec requête {i+1}")
            return False
        
        time.sleep(0.1)  # Petite pause
    
    print("   ✅ Toutes les requêtes multiples réussies")
    return True

if __name__ == "__main__":
    flow_ok = test_complete_frontend_flow()
    multiple_ok = test_multiple_requests()
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS FINAUX:")
    print(f"   Flux complet: {'✅' if flow_ok else '❌'}")
    print(f"   Requêtes multiples: {'✅' if multiple_ok else '❌'}")
    
    if flow_ok and multiple_ok:
        print("\n🎉 TOUT FONCTIONNE PARFAITEMENT!")
        print("   L'application frontend devrait maintenant marcher sans problème.")
        print("\n🔗 Testez dans le navigateur:")
        print("   http://localhost:5173")
        print("   Identifiants: admin / admin123")
    else:
        print("\n❌ Il reste des problèmes à résoudre.")
