"""
Schémas pour les documents
"""

from pydantic import BaseModel
from typing import Optional, List
from datetime import datetime

class DocumentBase(BaseModel):
    """Schéma de base pour les documents"""
    title: str
    description: Optional[str] = None
    category_id: Optional[int] = None

class DocumentCreate(DocumentBase):
    """Schéma pour la création de document"""
    tag_names: Optional[List[str]] = []

class DocumentUpdate(BaseModel):
    """Schéma pour la mise à jour de document"""
    title: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[int] = None
    tag_names: Optional[List[str]] = None

class DocumentInDB(DocumentBase):
    """Schéma pour les documents en base de données"""
    id: int
    filename: str
    original_filename: str
    file_path: str
    file_size: int
    mime_type: str
    ocr_text: Optional[str] = None
    ocr_confidence: Optional[float] = None
    owner_id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class Document(DocumentInDB):
    """Schéma public pour les documents avec relations"""
    owner: Optional[dict] = None
    category: Optional[dict] = None
    tags: List[dict] = []
    file_size_mb: Optional[float] = None
    file_url: Optional[str] = None
