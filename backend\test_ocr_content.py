"""
Test pour voir le contenu OCR des documents existants
"""

import requests

def test_document_ocr():
    """Test pour voir le contenu OCR de tous les documents"""
    print("🔍 Analyse du contenu OCR des documents...")
    
    base_url = "http://localhost:8000"
    
    # Étape 1: Se connecter
    print("\n1️⃣ Connexion...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Échec connexion: {login_response.status_code}")
            return False
        
        token = login_response.json().get("access_token")
        print(f"✅ Token obtenu")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Étape 2: Récupérer tous les documents
        print("\n2️⃣ Récupération des documents...")
        
        docs_response = requests.get(
            f"{base_url}/api/documents/",
            headers=headers,
            timeout=10
        )
        
        if docs_response.status_code != 200:
            print(f"❌ Erreur récupération documents: {docs_response.status_code}")
            return False
        
        documents = docs_response.json()
        print(f"✅ {len(documents)} documents trouvés")
        
        if not documents:
            print("ℹ️ Aucun document à analyser")
            return True
        
        # Étape 3: Analyser chaque document
        print("\n3️⃣ Analyse du contenu OCR...")
        print("=" * 80)
        
        for i, doc in enumerate(documents, 1):
            doc_id = doc["id"]
            title = doc.get("title", "Sans titre")
            filename = doc.get("filename", "Sans nom")
            
            print(f"\n📄 Document {i}: {title}")
            print(f"   Fichier: {filename}")
            print(f"   ID: {doc_id}")
            print(f"   Taille: {doc.get('file_size_mb', 0)} MB")
            print(f"   Type: {doc.get('mime_type', 'Inconnu')}")
            
            # Récupérer le contenu OCR
            ocr_response = requests.get(
                f"{base_url}/api/documents/{doc_id}/ocr",
                headers=headers,
                timeout=10
            )
            
            if ocr_response.status_code == 200:
                ocr_data = ocr_response.json()
                
                if ocr_data.get("has_ocr"):
                    ocr_text = ocr_data.get("ocr_text", "")
                    confidence = ocr_data.get("ocr_confidence")
                    
                    print(f"   ✅ OCR disponible (confiance: {confidence}%)")
                    print(f"   📝 Contenu OCR ({len(ocr_text)} caractères):")
                    
                    # Afficher les premières lignes du contenu
                    lines = ocr_text.split('\n')[:5]
                    for line in lines:
                        if line.strip():
                            print(f"      {line.strip()[:80]}...")
                    
                    if len(lines) > 5:
                        print(f"      ... ({len(ocr_text.split())} mots au total)")
                else:
                    print(f"   ❌ Pas de contenu OCR")
            else:
                print(f"   ❌ Erreur récupération OCR: {ocr_response.status_code}")
            
            print("-" * 80)
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_search_in_ocr():
    """Test de recherche dans le contenu OCR"""
    print("\n🔍 Test de recherche dans le contenu OCR...")
    
    base_url = "http://localhost:8000"
    
    # Se connecter
    login_response = requests.post(
        f"{base_url}/api/auth/auth",
        json={"username": "admin", "password": "admin123"},
        timeout=10
    )
    
    if login_response.status_code != 200:
        print("❌ Échec connexion pour recherche")
        return False
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Termes de recherche courants
    search_terms = ["facture", "contrat", "pdf", "document", "2025", "euro", "€"]
    
    print("\n🔍 Recherche de termes courants:")
    
    for term in search_terms:
        try:
            search_response = requests.get(
                f"{base_url}/api/search/?q={term}",
                headers=headers,
                timeout=10
            )
            
            if search_response.status_code == 200:
                results = search_response.json()
                print(f"   '{term}': {len(results)} document(s) trouvé(s)")
                
                if results:
                    for doc in results[:2]:  # Afficher les 2 premiers
                        print(f"      - {doc.get('title', 'Sans titre')}")
            else:
                print(f"   '{term}': Erreur {search_response.status_code}")
                
        except Exception as e:
            print(f"   '{term}': Erreur {e}")
    
    return True

def show_search_tips():
    """Affiche des conseils pour utiliser la recherche"""
    print("\n💡 CONSEILS POUR UTILISER LA RECHERCHE:")
    print("=" * 60)
    print("🔍 Types de recherche disponibles:")
    print("   • Recherche dans le TITRE des documents")
    print("   • Recherche dans la DESCRIPTION")
    print("   • Recherche dans le CONTENU OCR (texte extrait)")
    print("   • Recherche dans le NOM DU FICHIER")
    print()
    print("📝 Exemples de recherche:")
    print("   • 'facture' → Trouve tous les documents contenant 'facture'")
    print("   • 'contrat 2025' → Trouve les contrats de 2025")
    print("   • 'PDF' → Trouve tous les fichiers PDF")
    print("   • 'euro' ou '€' → Trouve les documents avec des montants")
    print()
    print("🏷️ Filtres disponibles:")
    print("   • Par catégorie (Factures, Contrats, etc.)")
    print("   • Par date de création")
    print("   • Par propriétaire (pour les admins)")
    print()
    print("🌐 Comment utiliser dans l'interface web:")
    print("   1. Connectez-vous sur http://localhost:5173")
    print("   2. Utilisez la barre de recherche en haut")
    print("   3. Cliquez sur les filtres pour affiner")
    print("   4. Cliquez sur un document pour voir son contenu OCR")

if __name__ == "__main__":
    print("🚀 Analyse du contenu OCR et recherche")
    print("=" * 60)
    
    ocr_ok = test_document_ocr()
    
    if ocr_ok:
        search_ok = test_search_in_ocr()
        show_search_tips()
        
        print("\n🎉 ANALYSE TERMINÉE!")
        print("Vous pouvez maintenant:")
        print("   • Voir le contenu OCR de chaque document")
        print("   • Rechercher dans le texte extrait")
        print("   • Utiliser tous les filtres disponibles")
    else:
        print("\n❌ Problème lors de l'analyse")
