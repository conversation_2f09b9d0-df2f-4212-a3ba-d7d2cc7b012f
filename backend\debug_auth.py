"""
Diagnostic complet de l'authentification
"""

import requests
import json
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from app.models.user import User
from app.core.security import verify_password, create_access_token, verify_token

def test_database_users():
    """Test des utilisateurs en base"""
    print("🗄️ Test des utilisateurs en base de données...")
    
    database_url = "mysql+pymysql://root:@localhost:3306/archivage_db"
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    db = SessionLocal()
    
    try:
        users = db.query(User).all()
        print(f"📊 {len(users)} utilisateur(s) trouvé(s):")
        
        for user in users:
            print(f"  - ID: {user.id}")
            print(f"    Username: '{user.username}'")
            print(f"    Email: {user.email}")
            print(f"    Role: {user.role}")
            print(f"    Active: {user.is_active}")
            print(f"    Password hash: {user.hashed_password[:50]}...")
            
            # Test du mot de passe
            if user.username == "admin":
                pwd_ok = verify_password("admin123", user.hashed_password)
                print(f"    Password 'admin123' OK: {pwd_ok}")
            elif user.username == "testuser":
                pwd_ok = verify_password("test123", user.hashed_password)
                print(f"    Password 'test123' OK: {pwd_ok}")
            print()
            
    except Exception as e:
        print(f"❌ Erreur base de données: {e}")
    finally:
        db.close()

def test_token_generation():
    """Test de génération de token"""
    print("🔑 Test de génération de token...")
    
    try:
        # Générer un token pour admin
        token = create_access_token(data={"sub": "admin"})
        print(f"✅ Token généré: {token[:50]}...")
        
        # Vérifier le token
        username = verify_token(token)
        print(f"✅ Token vérifié, username: {username}")
        
        return token
        
    except Exception as e:
        print(f"❌ Erreur token: {e}")
        return None

def test_api_endpoints():
    """Test des endpoints API"""
    print("🌐 Test des endpoints API...")
    
    base_url = "http://localhost:8000"
    
    # Test health
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        print(f"Health endpoint: {response.status_code}")
    except Exception as e:
        print(f"❌ Health endpoint: {e}")
        return False
    
    # Test login
    try:
        login_data = {
            "username": "admin",
            "password": "admin123"
        }
        
        print(f"🔐 Test login avec: {login_data}")
        
        response = requests.post(
            f"{base_url}/api/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"Login status: {response.status_code}")
        print(f"Login response: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"✅ Token reçu: {token[:50]}...")
            
            # Test /me endpoint
            headers = {"Authorization": f"Bearer {token}"}
            me_response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=10)
            
            print(f"Me endpoint status: {me_response.status_code}")
            print(f"Me endpoint response: {me_response.text}")
            
            return True
        else:
            print(f"❌ Login failed: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ API test error: {e}")
        return False

def test_frontend_login():
    """Test de login depuis le frontend"""
    print("🖥️ Test login frontend...")
    
    try:
        # Simuler une requête frontend
        response = requests.post(
            "http://localhost:8000/api/auth/login",
            json={"username": "admin", "password": "admin123"},  # JSON au lieu de form-data
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"Frontend JSON login status: {response.status_code}")
        print(f"Frontend JSON login response: {response.text}")
        
    except Exception as e:
        print(f"❌ Frontend test error: {e}")

def check_cors_and_headers():
    """Vérifier CORS et headers"""
    print("🌍 Test CORS et headers...")
    
    try:
        # Test OPTIONS request (preflight CORS)
        response = requests.options(
            "http://localhost:8000/api/auth/login",
            headers={
                "Origin": "http://localhost:5173",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            },
            timeout=5
        )
        
        print(f"CORS preflight status: {response.status_code}")
        print(f"CORS headers: {dict(response.headers)}")
        
    except Exception as e:
        print(f"❌ CORS test error: {e}")

if __name__ == "__main__":
    print("🔍 DIAGNOSTIC COMPLET D'AUTHENTIFICATION")
    print("=" * 60)
    
    # Tests
    test_database_users()
    print()
    
    token = test_token_generation()
    print()
    
    api_ok = test_api_endpoints()
    print()
    
    test_frontend_login()
    print()
    
    check_cors_and_headers()
    print()
    
    print("📋 RÉSUMÉ:")
    if api_ok:
        print("✅ L'API backend fonctionne")
        print("✅ L'authentification fonctionne")
        print("💡 Le problème vient probablement du frontend")
    else:
        print("❌ Problème avec l'API backend")
        print("💡 Vérifiez les logs du serveur backend")
    
    print("\n🔧 SOLUTIONS POSSIBLES:")
    print("1. Vérifiez que vous utilisez les bons identifiants")
    print("2. Vérifiez la console du navigateur pour les erreurs")
    print("3. Vérifiez que le backend est accessible depuis le frontend")
    print("4. Vérifiez les paramètres CORS")
