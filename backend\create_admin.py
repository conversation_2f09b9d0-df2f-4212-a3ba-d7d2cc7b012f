"""
Script pour créer un utilisateur administrateur par défaut
"""

from sqlalchemy.orm import Session
from app.core.database import SessionLocal
from app.models.user import User, UserRole
from app.core.security import get_password_hash

def create_admin_user():
    """Crée un utilisateur administrateur par défaut"""
    
    db = SessionLocal()
    
    try:
        # Vérifier si l'admin existe déjà
        existing_admin = db.query(User).filter(User.username == "admin").first()
        if existing_admin:
            print("L'utilisateur admin existe déjà.")
            print(f"Email: {existing_admin.email}")
            print(f"Username: {existing_admin.username}")
            print(f"Role: {existing_admin.role}")
            return existing_admin
        
        # Créer l'utilisateur admin
        admin_user = User(
            email="<EMAIL>",
            username="admin",
            first_name="Admin",
            last_name="Syst<PERSON>",
            hashed_password=get_password_hash("admin123"),
            role=UserRole.ADMIN,
            is_active=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print("✅ Utilisateur administrateur créé avec succès !")
        print(f"Email: {admin_user.email}")
        print(f"Username: {admin_user.username}")
        print(f"Mot de passe: admin123")
        print(f"Role: {admin_user.role}")
        
        return admin_user
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'admin : {e}")
        db.rollback()
        return None
    finally:
        db.close()

def create_test_user():
    """Crée un utilisateur de test"""
    
    db = SessionLocal()
    
    try:
        # Vérifier si l'utilisateur test existe déjà
        existing_user = db.query(User).filter(User.username == "testuser").first()
        if existing_user:
            print("L'utilisateur test existe déjà.")
            return existing_user
        
        # Créer l'utilisateur test
        test_user = User(
            email="<EMAIL>",
            username="testuser",
            first_name="Test",
            last_name="User",
            hashed_password=get_password_hash("test123"),
            role=UserRole.USER,
            is_active=True
        )
        
        db.add(test_user)
        db.commit()
        db.refresh(test_user)
        
        print("✅ Utilisateur test créé avec succès !")
        print(f"Email: {test_user.email}")
        print(f"Username: {test_user.username}")
        print(f"Mot de passe: test123")
        print(f"Role: {test_user.role}")
        
        return test_user
        
    except Exception as e:
        print(f"❌ Erreur lors de la création de l'utilisateur test : {e}")
        db.rollback()
        return None
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Création des utilisateurs par défaut...")
    print("=" * 50)
    
    # Créer l'admin
    admin = create_admin_user()
    print()
    
    # Créer l'utilisateur test
    user = create_test_user()
    print()
    
    if admin and user:
        print("✅ Tous les utilisateurs ont été créés avec succès !")
        print("\n📋 Récapitulatif des comptes :")
        print("Admin:")
        print("  - Email: <EMAIL>")
        print("  - Username: admin")
        print("  - Password: admin123")
        print("\nUtilisateur:")
        print("  - Email: <EMAIL>")
        print("  - Username: testuser")
        print("  - Password: test123")
    else:
        print("❌ Erreur lors de la création des utilisateurs.")
