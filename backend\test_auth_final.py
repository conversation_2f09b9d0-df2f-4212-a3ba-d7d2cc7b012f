"""
Test final de l'authentification complète
"""

import requests
import json

def test_complete_auth():
    """Test complet de l'authentification"""
    print("🔐 TEST COMPLET D'AUTHENTIFICATION")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test 1: Login avec JSON (comme le frontend)
    print("\n1️⃣ Test login avec JSON...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/login",
            json={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"   ✅ Login JSON réussi!")
            print(f"   Token: {token[:30]}...")
            
            # Test du profil avec ce token
            print("\n2️⃣ Test récupération profil...")
            headers = {"Authorization": f"Bearer {token}"}
            me_response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=10)
            
            print(f"   Status: {me_response.status_code}")
            
            if me_response.status_code == 200:
                profile = me_response.json()
                print(f"   ✅ Profil récupéré: {profile.get('username')}")
                print(f"   Email: {profile.get('email')}")
                print(f"   Role: {profile.get('role')}")
                return True
            else:
                print(f"   ❌ Erreur profil: {me_response.text}")
                return False
        else:
            print(f"   ❌ Erreur login: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_form_data_login():
    """Test avec form-data (pour compatibilité)"""
    print("\n3️⃣ Test login avec form-data...")
    
    base_url = "http://localhost:8000"
    
    try:
        response = requests.post(
            f"{base_url}/api/auth/login",
            data={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 200:
            print(f"   ✅ Login form-data réussi!")
            return True
        else:
            print(f"   ❌ Erreur: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_wrong_credentials():
    """Test avec de mauvais identifiants"""
    print("\n4️⃣ Test avec mauvais identifiants...")
    
    base_url = "http://localhost:8000"
    
    try:
        response = requests.post(
            f"{base_url}/api/auth/login",
            json={"username": "admin", "password": "wrongpassword"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 401:
            print(f"   ✅ Rejet correct des mauvais identifiants")
            return True
        else:
            print(f"   ❌ Devrait rejeter: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    # Tests
    json_ok = test_complete_auth()
    form_ok = test_form_data_login()
    reject_ok = test_wrong_credentials()
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS:")
    print(f"   Login JSON: {'✅' if json_ok else '❌'}")
    print(f"   Login Form: {'✅' if form_ok else '❌'}")
    print(f"   Rejet mauvais ID: {'✅' if reject_ok else '❌'}")
    
    if json_ok and form_ok and reject_ok:
        print("\n🎉 AUTHENTIFICATION COMPLÈTEMENT FONCTIONNELLE!")
        print("   Le frontend devrait maintenant marcher parfaitement.")
    else:
        print("\n❌ Il reste des problèmes à corriger.")
    
    print("\n🔧 Pour tester dans le navigateur:")
    print("   1. Allez sur http://localhost:5173")
    print("   2. Utilisez: admin / admin123")
    print("   3. Ou: testuser / test123")
