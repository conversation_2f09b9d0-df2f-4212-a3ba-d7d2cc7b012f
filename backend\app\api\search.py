"""
Routes pour la recherche de documents
"""

from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.orm import Session
from sqlalchemy import or_, and_, func
from ..core.database import get_db
from ..core.security import get_current_active_user
from ..models.document import Document, Tag, Category
from ..models.user import User
from ..schemas.document import Document as DocumentSchema
from ..services.file_service import file_service

router = APIRouter()

@router.get("/")
async def search_documents(
    q: Optional[str] = Query(None, description="Terme de recherche"),
    category_id: Optional[int] = Query(None, description="ID de la catégorie"),
    tags: Optional[List[str]] = Query(None, description="Liste des tags"),
    owner_id: Optional[int] = Query(None, description="ID du propriétaire"),
    date_from: Optional[str] = Query(None, description="Date de début (YYYY-MM-DD)"),
    date_to: Optional[str] = Query(None, description="Date de fin (YYYY-MM-DD)"),
    sort_by: Optional[str] = Query("created_at", description="Champ de tri"),
    sort_order: Optional[str] = Query("desc", description="Ordre de tri (asc/desc)"),
    skip: int = Query(0, description="Nombre d'éléments à ignorer"),
    limit: int = Query(100, description="Nombre maximum d'éléments à retourner"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Recherche avancée de documents"""

    try:
        print(f"🔍 Recherche: q='{q}', category_id={category_id}, user={current_user.username}")

        # Construire la requête de base
        query = db.query(Document)

        # Les utilisateurs non-admin ne peuvent voir que leurs propres documents
        from ..models.user import UserRole
        if current_user.role != UserRole.ADMIN:
            query = query.filter(Document.owner_id == current_user.id)
        elif owner_id:
            query = query.filter(Document.owner_id == owner_id)

        # Recherche textuelle
        if q:
            search_term = f"%{q}%"
            query = query.filter(
                or_(
                    Document.title.ilike(search_term),
                    Document.description.ilike(search_term),
                    Document.ocr_text.ilike(search_term),
                    Document.original_filename.ilike(search_term)
                )
            )

        # Filtrer par catégorie
        if category_id:
            query = query.filter(Document.category_id == category_id)

        # Filtrer par tags
        if tags:
            query = query.join(Document.tags).filter(Tag.name.in_(tags))

        # Filtrer par date
        if date_from:
            query = query.filter(Document.created_at >= date_from)
        if date_to:
            query = query.filter(Document.created_at <= date_to)

        # Tri
        if sort_by and hasattr(Document, sort_by):
            order_column = getattr(Document, sort_by)
            if sort_order.lower() == "desc":
                query = query.order_by(order_column.desc())
            else:
                query = query.order_by(order_column.asc())
        else:
            query = query.order_by(Document.created_at.desc())

        # Pagination
        documents = query.offset(skip).limit(limit).all()

        print(f"✅ {len(documents)} documents trouvés")

        # Convertir chaque document en dictionnaire enrichi
        from ..api.documents import enrich_document_dict
        result = []
        for doc in documents:
            doc_dict = enrich_document_dict(doc, current_user)
            result.append(doc_dict)

        return result

    except Exception as e:
        print(f"❌ Erreur lors de la recherche: {e}")
        import traceback
        traceback.print_exc()
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors de la recherche: {str(e)}"
        )

@router.get("/suggestions")
async def get_search_suggestions(
    q: str = Query(..., description="Terme de recherche partiel"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Suggestions de recherche basées sur les titres et tags existants"""

    try:
        search_term = f"%{q}%"

        # Suggestions de titres
        title_query = db.query(Document.title).filter(
            Document.title.ilike(search_term)
        )

        # Les utilisateurs non-admin ne voient que leurs documents
        from ..models.user import UserRole
        if current_user.role != UserRole.ADMIN:
            title_query = title_query.filter(Document.owner_id == current_user.id)

        titles = [row[0] for row in title_query.limit(5).all() if row[0]]

        # Suggestions de tags
        tag_suggestions = db.query(Tag.name).filter(
            Tag.name.ilike(search_term)
        ).limit(5).all()
        tags = [row[0] for row in tag_suggestions if row[0]]

        # Suggestions de catégories
        category_suggestions = db.query(Category.name).filter(
            Category.name.ilike(search_term)
        ).limit(5).all()
        categories = [row[0] for row in category_suggestions if row[0]]

        return {
            "titles": titles,
            "tags": tags,
            "categories": categories
        }

    except Exception as e:
        print(f"❌ Erreur suggestions: {e}")
        import traceback
        traceback.print_exc()
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors des suggestions: {str(e)}"
        )

@router.get("/stats")
async def get_search_stats(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """Statistiques de recherche et du système"""

    try:
        from ..models.user import UserRole

        # Requête de base selon les permissions
        base_query = db.query(Document)
        if current_user.role != UserRole.ADMIN:
            base_query = base_query.filter(Document.owner_id == current_user.id)

        # Nombre total de documents
        total_documents = base_query.count()

        # Documents par catégorie (simplifié)
        categories = []
        try:
            category_stats = db.query(
                Category.name,
                func.count(Document.id).label('count')
            ).outerjoin(Document).group_by(Category.id, Category.name)

            if current_user.role != UserRole.ADMIN:
                category_stats = category_stats.filter(
                    or_(Document.owner_id == current_user.id, Document.id.is_(None))
                )

            categories = [
                {"name": name or "Sans catégorie", "count": count or 0}
                for name, count in category_stats.all()
            ]
        except Exception as e:
            print(f"⚠️ Erreur stats catégories: {e}")
            categories = []

        # Tags les plus utilisés (simplifié)
        top_tags = []
        try:
            # Requête simplifiée pour éviter les erreurs de jointure
            all_tags = db.query(Tag.name).limit(10).all()
            top_tags = [{"name": tag[0], "count": 1} for tag in all_tags if tag[0]]
        except Exception as e:
            print(f"⚠️ Erreur stats tags: {e}")
            top_tags = []

        # Documents récents
        recent_docs_list = []
        try:
            recent_documents = base_query.order_by(
                Document.created_at.desc()
            ).limit(5).all()

            # Convertir les documents récents en dictionnaires
            from ..api.documents import enrich_document_dict
            for doc in recent_documents:
                doc_dict = enrich_document_dict(doc, current_user)
                recent_docs_list.append(doc_dict)
        except Exception as e:
            print(f"⚠️ Erreur documents récents: {e}")
            recent_docs_list = []

        return {
            "total_documents": total_documents,
            "categories": categories,
            "top_tags": top_tags,
            "recent_documents": recent_docs_list
        }

    except Exception as e:
        print(f"❌ Erreur stats: {e}")
        import traceback
        traceback.print_exc()
        from fastapi import HTTPException, status
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Erreur lors des statistiques: {str(e)}"
        )
