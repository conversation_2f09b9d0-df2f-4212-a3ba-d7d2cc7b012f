/**
 * Service API pour communiquer avec le backend
 */

import axios from 'axios';

// Configuration de base d'Axios
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Intercepteur pour ajouter le token d'authentification
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('🔑 Token ajouté à la requête:', config.url);
    } else {
      console.log('⚠️ Pas de token pour:', config.url);
    }
    return config;
  },
  (error) => {
    console.error('❌ Erreur intercepteur request:', error);
    return Promise.reject(error);
  }
);

// Intercepteur pour gérer les erreurs de réponse
api.interceptors.response.use(
  (response) => {
    console.log('✅ Réponse API:', response.config.url, response.status);
    return response;
  },
  (error) => {
    console.error('❌ Erreur API:', error.config?.url, error.response?.status, error.response?.data);

    if (error.response?.status === 401) {
      console.log('🔒 Token expiré ou invalide, redirection vers login');
      // Token expiré ou invalide
      localStorage.removeItem('token');
      localStorage.removeItem('user');

      // Éviter la redirection si on est déjà sur la page de login
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }

    if (error.response?.status === 403) {
      console.log('🚫 Accès refusé (403)');
    }

    return Promise.reject(error);
  }
);

// Services d'authentification
export const authAPI = {
  login: async (username, password) => {
    console.log('🔐 Tentative de connexion:', username);
    const response = await api.post('/api/auth/auth', {
      username,
      password
    });
    console.log('✅ Connexion réussie');
    return response.data;
  },

  register: async (userData) => {
    const response = await api.post('/api/auth/register', userData);
    return response.data;
  },

  getCurrentUser: async () => {
    const response = await api.get('/api/auth/me');
    return response.data;
  },

  refreshToken: async () => {
    const response = await api.post('/api/auth/refresh');
    return response.data;
  },
};

// Services utilisateurs
export const userAPI = {
  getUsers: async (skip = 0, limit = 100) => {
    const response = await api.get(`/api/users/?skip=${skip}&limit=${limit}`);
    return response.data;
  },

  getUser: async (userId) => {
    const response = await api.get(`/api/users/${userId}`);
    return response.data;
  },

  updateUser: async (userId, userData) => {
    const response = await api.put(`/api/users/${userId}`, userData);
    return response.data;
  },

  deleteUser: async (userId) => {
    const response = await api.delete(`/api/users/${userId}`);
    return response.data;
  },
};

// Services documents
export const documentAPI = {
  uploadDocument: async (file, metadata) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('title', metadata.title);
    
    if (metadata.description) {
      formData.append('description', metadata.description);
    }
    if (metadata.category_id) {
      formData.append('category_id', metadata.category_id);
    }
    if (metadata.tags && metadata.tags.length > 0) {
      formData.append('tag_names', metadata.tags.join(','));
    }

    const response = await api.post('/api/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  },

  getDocuments: async (params = {}) => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        queryParams.append(key, value);
      }
    });

    const response = await api.get(`/api/documents/?${queryParams}`);
    return response.data;
  },

  getDocument: async (documentId) => {
    const response = await api.get(`/api/documents/${documentId}`);
    return response.data;
  },

  updateDocument: async (documentId, updateData) => {
    const response = await api.put(`/api/documents/${documentId}`, updateData);
    return response.data;
  },

  deleteDocument: async (documentId) => {
    const response = await api.delete(`/api/documents/${documentId}`);
    return response.data;
  },

  getCategories: async () => {
    const response = await api.get('/api/documents/categories/');
    return response.data;
  },

  getTags: async () => {
    const response = await api.get('/api/documents/tags/');
    return response.data;
  },
};

// Services de recherche
export const searchAPI = {
  searchDocuments: async (params = {}) => {
    const queryParams = new URLSearchParams();
    
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(v => queryParams.append(key, v));
        } else {
          queryParams.append(key, value);
        }
      }
    });

    const response = await api.get(`/api/search/?${queryParams}`);
    return response.data;
  },

  getSuggestions: async (query) => {
    const response = await api.get(`/api/search/suggestions?q=${encodeURIComponent(query)}`);
    return response.data;
  },

  getStats: async () => {
    const response = await api.get('/api/search/stats');
    return response.data;
  },
};

export default api;
