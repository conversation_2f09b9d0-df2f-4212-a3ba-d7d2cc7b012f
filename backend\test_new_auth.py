"""
Test du nouvel endpoint d'authentification
"""

import requests

def test_new_auth():
    """Test du nouvel endpoint /api/auth/auth"""
    print("🔐 Test du nouvel endpoint /api/auth/auth")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    # Test avec JSON
    print("\n1️⃣ Test login JSON...")
    try:
        response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            token_data = response.json()
            token = token_data.get("access_token")
            print(f"   ✅ Login réussi!")
            print(f"   Token: {token[:30]}...")
            
            # Test du profil
            print("\n2️⃣ Test profil...")
            headers = {"Authorization": f"Bearer {token}"}
            me_response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=10)
            
            print(f"   Status: {me_response.status_code}")
            
            if me_response.status_code == 200:
                profile = me_response.json()
                print(f"   ✅ Profil: {profile.get('username')} ({profile.get('role')})")
                return True
            else:
                print(f"   ❌ Erreur profil: {me_response.text}")
                return False
        else:
            print(f"   ❌ Erreur login")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

def test_wrong_password():
    """Test avec mauvais mot de passe"""
    print("\n3️⃣ Test mauvais mot de passe...")
    
    try:
        response = requests.post(
            "http://localhost:8000/api/auth/auth",
            json={"username": "admin", "password": "wrongpassword"},
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        print(f"   Status: {response.status_code}")
        
        if response.status_code == 401:
            print(f"   ✅ Rejet correct")
            return True
        else:
            print(f"   ❌ Devrait rejeter")
            return False
            
    except Exception as e:
        print(f"   ❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    auth_ok = test_new_auth()
    reject_ok = test_wrong_password()
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS:")
    print(f"   Authentification: {'✅' if auth_ok else '❌'}")
    print(f"   Rejet mauvais MDP: {'✅' if reject_ok else '❌'}")
    
    if auth_ok and reject_ok:
        print("\n🎉 AUTHENTIFICATION FONCTIONNELLE!")
        print("   Testez maintenant dans le navigateur:")
        print("   http://localhost:5173")
        print("   admin / admin123")
    else:
        print("\n❌ Problème persistant")
