"""
Test de récupération de la liste des documents
"""

import requests

def test_documents_list():
    """Test de récupération de la liste des documents"""
    print("📋 Test de récupération des documents...")
    
    base_url = "http://localhost:8000"
    
    # Étape 1: Se connecter
    print("\n1️⃣ Connexion...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Échec connexion: {login_response.status_code}")
            return False
        
        token = login_response.json().get("access_token")
        print(f"✅ Token obtenu: {token[:30]}...")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Étape 2: Récupérer la liste des documents
        print("\n2️⃣ Récupération des documents...")
        
        docs_response = requests.get(
            f"{base_url}/api/documents/",
            headers=headers,
            timeout=10
        )
        
        print(f"Status: {docs_response.status_code}")
        
        if docs_response.status_code == 200:
            documents = docs_response.json()
            print(f"✅ Documents récupérés: {len(documents)} documents")
            
            if documents:
                print("\n📄 Premier document:")
                doc = documents[0]
                for key, value in doc.items():
                    if isinstance(value, str) and len(value) > 50:
                        value = value[:50] + "..."
                    print(f"   {key}: {value}")
            else:
                print("ℹ️ Aucun document trouvé")
            
            return True
        else:
            print(f"❌ Erreur: {docs_response.status_code}")
            print(f"Response: {docs_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_categories():
    """Test de récupération des catégories"""
    print("\n📁 Test de récupération des catégories...")
    
    base_url = "http://localhost:8000"
    
    # Se connecter
    login_response = requests.post(
        f"{base_url}/api/auth/auth",
        json={"username": "admin", "password": "admin123"},
        timeout=10
    )
    
    if login_response.status_code != 200:
        print("❌ Échec connexion pour test catégories")
        return False
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        cats_response = requests.get(
            f"{base_url}/api/documents/categories/",
            headers=headers,
            timeout=10
        )
        
        print(f"Status: {cats_response.status_code}")
        
        if cats_response.status_code == 200:
            categories = cats_response.json()
            print(f"✅ Catégories récupérées: {len(categories)} catégories")
            
            for cat in categories:
                print(f"   - {cat.get('name')} ({cat.get('color')})")
            
            return True
        else:
            print(f"❌ Erreur catégories: {cats_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_health():
    """Test de santé de l'API"""
    print("\n🏥 Test de santé de l'API...")
    
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        print(f"Health Status: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ API en bonne santé")
            return True
        else:
            print("❌ Problème avec l'API")
            return False
            
    except Exception as e:
        print(f"❌ Erreur health: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test de chargement des documents")
    print("=" * 50)
    
    health_ok = test_health()
    docs_ok = test_documents_list()
    cats_ok = test_categories()
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS:")
    print(f"   API Health: {'✅' if health_ok else '❌'}")
    print(f"   Documents: {'✅' if docs_ok else '❌'}")
    print(f"   Catégories: {'✅' if cats_ok else '❌'}")
    
    if docs_ok and cats_ok:
        print("\n🎉 CHARGEMENT DES DOCUMENTS FONCTIONNEL!")
        print("L'erreur de chargement devrait être corrigée.")
    else:
        print("\n❌ Problèmes de chargement détectés")
        print("Vérifiez les logs du serveur backend.")
