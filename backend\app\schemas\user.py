"""
Schémas pour les utilisateurs
"""

from pydantic import BaseModel, EmailStr
from typing import Optional
from datetime import datetime
from ..models.user import UserRole

class UserBase(BaseModel):
    """Schéma de base pour les utilisateurs"""
    email: EmailStr
    username: str
    first_name: str
    last_name: str
    role: UserRole = UserRole.USER
    is_active: bool = True

class UserCreate(BaseModel):
    """Schéma pour la création d'utilisateur"""
    email: EmailStr
    username: str
    first_name: str
    last_name: str
    password: str

class UserUpdate(BaseModel):
    """Schéma pour la mise à jour d'utilisateur"""
    email: Optional[EmailStr] = None
    username: Optional[str] = None
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    role: Optional[UserRole] = None
    is_active: Optional[bool] = None
    password: Optional[str] = None

class UserInDB(UserBase):
    """Schéma pour les utilisateurs en base de données"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class User(UserInDB):
    """Schéma public pour les utilisateurs (sans mot de passe)"""
    pass
