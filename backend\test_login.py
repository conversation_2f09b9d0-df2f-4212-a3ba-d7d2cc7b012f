"""
Test de connexion API
"""

import requests
import json

def test_api_login():
    """Test de connexion via l'API"""
    print("🔐 Test de connexion API...")
    
    base_url = "http://localhost:8000"
    
    # Test avec les bons identifiants
    print("\n👤 Test connexion admin...")
    login_data = {
        "username": "admin",  # Attention : pas "adamin" !
        "password": "admin123"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/auth/login",
            data=login_data,
            headers={"Content-Type": "application/x-www-form-urlencoded"},
            timeout=10
        )
        
        print(f"Status: {response.status_code}")
        
        if response.status_code == 200:
            token_data = response.json()
            print(f"✅ Connexion réussie !")
            print(f"Token reçu: {token_data.get('access_token')[:50]}...")
            
            # Test du profil
            token = token_data.get('access_token')
            headers = {"Authorization": f"Bearer {token}"}
            
            profile_response = requests.get(f"{base_url}/api/auth/me", headers=headers, timeout=10)
            
            if profile_response.status_code == 200:
                profile = profile_response.json()
                print(f"✅ Profil récupéré: {profile.get('username')} ({profile.get('email')})")
                print(f"   Rôle: {profile.get('role')}")
            else:
                print(f"❌ Erreur profil: {profile_response.status_code}")
                print(f"   Response: {profile_response.text}")
                
        else:
            print(f"❌ Erreur de connexion: {response.status_code}")
            print(f"   Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Impossible de se connecter au serveur")
        print("   Vérifiez que le serveur backend est démarré sur le port 8000")
    except Exception as e:
        print(f"❌ Erreur: {e}")

if __name__ == "__main__":
    print("🚀 Test de connexion API")
    print("=" * 40)
    
    test_api_login()
    
    print("\n📋 Identifiants corrects :")
    print("   Username: admin (pas 'adamin' !)")
    print("   Password: admin123")
