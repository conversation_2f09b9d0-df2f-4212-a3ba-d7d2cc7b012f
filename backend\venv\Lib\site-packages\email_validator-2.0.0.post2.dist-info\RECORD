../../Scripts/email_validator.exe,sha256=66xikCqX1GmhdPmZiAE94ertuXQVZj2pbMET-qr26zg,108452
email_validator-2.0.0.post2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
email_validator-2.0.0.post2.dist-info/LICENSE,sha256=ogEPNDSH0_dhiv_lT3ifVIdgIzHAqNA_SemnxUfPBJk,7048
email_validator-2.0.0.post2.dist-info/METADATA,sha256=ZIdyrE5EFPXTpYEGRmQSR1aRvtKCPZ4AvDtVqPWv51Q,25413
email_validator-2.0.0.post2.dist-info/RECORD,,
email_validator-2.0.0.post2.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
email_validator-2.0.0.post2.dist-info/WHEEL,sha256=G16H4A3IeoQmnOrYV4ueZGKSjhipXx8zc8nu9FGlvMA,92
email_validator-2.0.0.post2.dist-info/entry_points.txt,sha256=zRM_6bNIUSHTbNx5u6M3nK1MAguvryrc9hICC6HyrBg,66
email_validator-2.0.0.post2.dist-info/top_level.txt,sha256=fYDOSWFZke46ut7WqdOAJjjhlpPYAaOwOwIsh3s8oWI,16
email_validator/__init__.py,sha256=4XQoz2JDUsvdDuvOqU7LV23QJrqwqYfycv4sVBK6BqA,4189
email_validator/__main__.py,sha256=SgarDcfH3W5KlcuUi6aaiQPqMdL3C-mOZVnTS6WesS4,2146
email_validator/__pycache__/__init__.cpython-37.pyc,,
email_validator/__pycache__/__main__.cpython-37.pyc,,
email_validator/__pycache__/deliverability.cpython-37.pyc,,
email_validator/__pycache__/exceptions_types.cpython-37.pyc,,
email_validator/__pycache__/rfc_constants.cpython-37.pyc,,
email_validator/__pycache__/syntax.cpython-37.pyc,,
email_validator/__pycache__/validate_email.cpython-37.pyc,,
email_validator/deliverability.py,sha256=it2lcdg-uML_-AVXOWQ6FbUW0bJjtViVEh-w2Im1E3g,5861
email_validator/exceptions_types.py,sha256=ruD1Xz-BLuagbfihbwSqFDNf9AHpfUwjG1oryoMwh6k,5524
email_validator/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
email_validator/rfc_constants.py,sha256=2yJVQgKFaVcG2CV-XT4PWevBUjAPVHRycgzOpBaqRZE,2720
email_validator/syntax.py,sha256=qr4QaQW_wP-_KR54ZDGjKeZYC9jx_GZ5X8sTX60RMf4,23969
email_validator/validate_email.py,sha256=17194Ndck9xtrhrXb5K_nLi_2m7SSq4tdGK2x--fr1A,9062
