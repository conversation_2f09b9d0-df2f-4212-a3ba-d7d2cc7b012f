"""
Test de la fonctionnalité de recherche
"""

import requests

def test_search():
    """Test de recherche de documents"""
    print("🔍 Test de recherche...")
    
    base_url = "http://localhost:8000"
    
    # Étape 1: Se connecter
    print("\n1️⃣ Connexion...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Échec connexion: {login_response.status_code}")
            return False
        
        token = login_response.json().get("access_token")
        print(f"✅ Token obtenu: {token[:30]}...")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Étape 2: Recherche sans paramètres (tous les documents)
        print("\n2️⃣ Recherche sans paramètres...")
        
        search_response = requests.get(
            f"{base_url}/api/search/",
            headers=headers,
            timeout=10
        )
        
        print(f"Search Status: {search_response.status_code}")
        
        if search_response.status_code == 200:
            documents = search_response.json()
            print(f"✅ {len(documents)} documents trouvés")
            
            if documents:
                print(f"   Premier document: {documents[0].get('title')}")
        else:
            print(f"❌ Erreur recherche: {search_response.text}")
            return False
        
        # Étape 3: Recherche avec terme
        print("\n3️⃣ Recherche avec terme...")
        
        search_term_response = requests.get(
            f"{base_url}/api/search/?q=pdf",
            headers=headers,
            timeout=10
        )
        
        print(f"Search Term Status: {search_term_response.status_code}")
        
        if search_term_response.status_code == 200:
            documents = search_term_response.json()
            print(f"✅ {len(documents)} documents trouvés avec 'pdf'")
        else:
            print(f"❌ Erreur recherche terme: {search_term_response.text}")
        
        # Étape 4: Recherche par catégorie
        print("\n4️⃣ Recherche par catégorie...")
        
        category_response = requests.get(
            f"{base_url}/api/search/?category_id=1",
            headers=headers,
            timeout=10
        )
        
        print(f"Category Search Status: {category_response.status_code}")
        
        if category_response.status_code == 200:
            documents = category_response.json()
            print(f"✅ {len(documents)} documents trouvés dans la catégorie 1")
        else:
            print(f"❌ Erreur recherche catégorie: {category_response.text}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_search_suggestions():
    """Test des suggestions de recherche"""
    print("\n💡 Test des suggestions...")
    
    base_url = "http://localhost:8000"
    
    # Se connecter
    login_response = requests.post(
        f"{base_url}/api/auth/auth",
        json={"username": "admin", "password": "admin123"},
        timeout=10
    )
    
    if login_response.status_code != 200:
        print("❌ Échec connexion pour suggestions")
        return False
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        suggestions_response = requests.get(
            f"{base_url}/api/search/suggestions?q=doc",
            headers=headers,
            timeout=10
        )
        
        print(f"Suggestions Status: {suggestions_response.status_code}")
        
        if suggestions_response.status_code == 200:
            suggestions = suggestions_response.json()
            print(f"✅ Suggestions récupérées:")
            print(f"   Titres: {len(suggestions.get('titles', []))}")
            print(f"   Tags: {len(suggestions.get('tags', []))}")
            print(f"   Catégories: {len(suggestions.get('categories', []))}")
            return True
        else:
            print(f"❌ Erreur suggestions: {suggestions_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

def test_search_stats():
    """Test des statistiques de recherche"""
    print("\n📊 Test des statistiques...")
    
    base_url = "http://localhost:8000"
    
    # Se connecter
    login_response = requests.post(
        f"{base_url}/api/auth/auth",
        json={"username": "admin", "password": "admin123"},
        timeout=10
    )
    
    if login_response.status_code != 200:
        print("❌ Échec connexion pour stats")
        return False
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    try:
        stats_response = requests.get(
            f"{base_url}/api/search/stats",
            headers=headers,
            timeout=10
        )
        
        print(f"Stats Status: {stats_response.status_code}")
        
        if stats_response.status_code == 200:
            stats = stats_response.json()
            print(f"✅ Statistiques récupérées:")
            print(f"   Total documents: {stats.get('total_documents')}")
            print(f"   Catégories: {len(stats.get('categories', []))}")
            print(f"   Top tags: {len(stats.get('top_tags', []))}")
            print(f"   Documents récents: {len(stats.get('recent_documents', []))}")
            return True
        else:
            print(f"❌ Erreur stats: {stats_response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test de la fonctionnalité de recherche")
    print("=" * 60)
    
    search_ok = test_search()
    suggestions_ok = test_search_suggestions()
    stats_ok = test_search_stats()
    
    print("\n" + "=" * 60)
    print("📊 RÉSULTATS:")
    print(f"   Recherche: {'✅' if search_ok else '❌'}")
    print(f"   Suggestions: {'✅' if suggestions_ok else '❌'}")
    print(f"   Statistiques: {'✅' if stats_ok else '❌'}")
    
    if search_ok and suggestions_ok and stats_ok:
        print("\n🎉 RECHERCHE FONCTIONNELLE!")
        print("L'erreur de recherche devrait être corrigée.")
    else:
        print("\n❌ Problèmes de recherche détectés")
        print("Vérifiez les logs du serveur backend.")
