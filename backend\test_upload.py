"""
Test de l'upload de documents
"""

import requests
import os

def test_upload():
    """Test de l'upload de documents"""
    print("📤 Test de l'upload de documents...")
    
    base_url = "http://localhost:8000"
    
    # Étape 1: Se connecter pour obtenir un token
    print("\n1️⃣ Connexion...")
    try:
        login_response = requests.post(
            f"{base_url}/api/auth/auth",
            json={"username": "admin", "password": "admin123"},
            timeout=10
        )
        
        if login_response.status_code != 200:
            print(f"❌ Échec connexion: {login_response.status_code}")
            return False
        
        token = login_response.json().get("access_token")
        print(f"✅ Token obtenu: {token[:30]}...")
        
        headers = {"Authorization": f"Bearer {token}"}
        
        # Étape 2: Créer un fichier de test
        print("\n2️⃣ Création d'un fichier de test...")
        test_content = """
        Ceci est un document de test pour l'upload.
        Il contient du texte simple pour tester l'OCR.
        Date: 2025-06-21
        Auteur: Test System
        """
        
        test_file_path = "test_document.txt"
        with open(test_file_path, "w", encoding="utf-8") as f:
            f.write(test_content)
        
        print(f"✅ Fichier créé: {test_file_path}")
        
        # Étape 3: Upload du fichier
        print("\n3️⃣ Upload du fichier...")
        
        with open(test_file_path, "rb") as f:
            files = {"file": ("test_document.txt", f, "text/plain")}
            data = {
                "title": "Document de test",
                "description": "Test d'upload via API"
            }
            
            upload_response = requests.post(
                f"{base_url}/api/documents/upload",
                headers=headers,
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"Upload Status: {upload_response.status_code}")
        
        if upload_response.status_code == 200:
            document = upload_response.json()
            print(f"✅ Upload réussi!")
            print(f"   ID: {document.get('id')}")
            print(f"   Titre: {document.get('title')}")
            print(f"   Taille: {document.get('file_size_mb')} MB")
            print(f"   URL: {document.get('file_url')}")
            
            # Nettoyage
            os.remove(test_file_path)
            print(f"🗑️ Fichier de test supprimé")
            
            return True
        else:
            print(f"❌ Erreur upload: {upload_response.status_code}")
            print(f"   Response: {upload_response.text}")
            
            # Nettoyage
            if os.path.exists(test_file_path):
                os.remove(test_file_path)
            
            return False
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
        
        # Nettoyage
        if os.path.exists(test_file_path):
            os.remove(test_file_path)
        
        return False

def test_upload_image():
    """Test d'upload d'une image"""
    print("\n📷 Test d'upload d'image...")
    
    base_url = "http://localhost:8000"
    
    # Se connecter
    login_response = requests.post(
        f"{base_url}/api/auth/auth",
        json={"username": "admin", "password": "admin123"},
        timeout=10
    )
    
    if login_response.status_code != 200:
        print("❌ Échec connexion pour test image")
        return False
    
    token = login_response.json().get("access_token")
    headers = {"Authorization": f"Bearer {token}"}
    
    # Créer une image de test simple (1x1 pixel PNG)
    import base64
    
    # PNG 1x1 pixel transparent
    png_data = base64.b64decode(
        "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg=="
    )
    
    test_image_path = "test_image.png"
    with open(test_image_path, "wb") as f:
        f.write(png_data)
    
    try:
        with open(test_image_path, "rb") as f:
            files = {"file": ("test_image.png", f, "image/png")}
            data = {
                "title": "Image de test",
                "description": "Test d'upload d'image"
            }
            
            upload_response = requests.post(
                f"{base_url}/api/documents/upload",
                headers=headers,
                files=files,
                data=data,
                timeout=30
            )
        
        print(f"Upload Image Status: {upload_response.status_code}")
        
        if upload_response.status_code == 200:
            print("✅ Upload d'image réussi!")
            return True
        else:
            print(f"❌ Erreur upload image: {upload_response.text}")
            return False
            
    finally:
        if os.path.exists(test_image_path):
            os.remove(test_image_path)

if __name__ == "__main__":
    print("🚀 Test d'upload de documents")
    print("=" * 50)
    
    text_ok = test_upload()
    image_ok = test_upload_image()
    
    print("\n" + "=" * 50)
    print("📊 RÉSULTATS:")
    print(f"   Upload texte: {'✅' if text_ok else '❌'}")
    print(f"   Upload image: {'✅' if image_ok else '❌'}")
    
    if text_ok and image_ok:
        print("\n🎉 UPLOAD FONCTIONNEL!")
    else:
        print("\n❌ Problèmes d'upload détectés")
