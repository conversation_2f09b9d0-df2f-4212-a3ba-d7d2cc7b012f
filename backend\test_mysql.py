"""
Test simple de connexion MySQL
"""

import pymysql
import sys

def test_mysql_connection():
    """Test basique de connexion MySQL"""
    print("🔌 Test de connexion MySQL...")
    
    # Paramètres de connexion par défaut XAMPP
    configs = [
        {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "",
            "name": "Défaut XAMPP (root sans mot de passe)"
        },
        {
            "host": "127.0.0.1",
            "port": 3306,
            "user": "root",
            "password": "",
            "name": "127.0.0.1 (root sans mot de passe)"
        },
        {
            "host": "localhost",
            "port": 3306,
            "user": "root",
            "password": "root",
            "name": "Root avec mot de passe 'root'"
        }
    ]
    
    for config in configs:
        print(f"\n🔍 Test: {config['name']}")
        try:
            connection = pymysql.connect(
                host=config["host"],
                port=config["port"],
                user=config["user"],
                password=config["password"],
                charset='utf8mb4'
            )
            
            with connection.cursor() as cursor:
                cursor.execute("SELECT VERSION()")
                version = cursor.fetchone()[0]
                print(f"✅ Connexion réussie ! MySQL version: {version}")
                
                # Lister les bases de données
                cursor.execute("SHOW DATABASES")
                databases = cursor.fetchall()
                print(f"📋 Bases de données disponibles: {len(databases)}")
                for db in databases:
                    print(f"   - {db[0]}")
            
            connection.close()
            
            # Si on arrive ici, la connexion fonctionne
            print(f"\n🎉 Configuration MySQL trouvée !")
            print(f"   Host: {config['host']}")
            print(f"   Port: {config['port']}")
            print(f"   User: {config['user']}")
            print(f"   Password: {'(vide)' if not config['password'] else '***'}")
            
            return config
            
        except Exception as e:
            print(f"❌ Échec: {e}")
    
    print("\n❌ Aucune configuration MySQL fonctionnelle trouvée")
    print("\n💡 Vérifiez que:")
    print("   - XAMPP est installé")
    print("   - MySQL est démarré dans XAMPP Control Panel")
    print("   - phpMyAdmin fonctionne (http://localhost/phpmyadmin)")
    
    return None

def create_database_simple(config):
    """Crée la base de données avec la configuration trouvée"""
    print(f"\n🗄️ Création de la base de données...")
    
    try:
        connection = pymysql.connect(
            host=config["host"],
            port=config["port"],
            user=config["user"],
            password=config["password"],
            charset='utf8mb4'
        )
        
        with connection.cursor() as cursor:
            # Créer la base de données
            cursor.execute("CREATE DATABASE IF NOT EXISTS archivage_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ Base de données 'archivage_db' créée")
            
            # Vérifier
            cursor.execute("SHOW DATABASES LIKE 'archivage_db'")
            result = cursor.fetchone()
            if result:
                print("✅ Base de données vérifiée")
            else:
                print("❌ Problème avec la création de la base")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test de connexion MySQL pour XAMPP")
    print("=" * 50)
    
    # Test de connexion
    working_config = test_mysql_connection()
    
    if working_config:
        # Créer la base de données
        if create_database_simple(working_config):
            print(f"\n✅ Configuration MySQL prête !")
            print(f"\n📝 Mettez à jour votre .env avec:")
            print(f"DATABASE_URL=mysql://{working_config['user']}:{working_config['password']}@{working_config['host']}:{working_config['port']}/archivage_db")
        else:
            print(f"\n❌ Problème lors de la création de la base")
    else:
        print(f"\n❌ Impossible de se connecter à MySQL")
        print(f"Vérifiez que MySQL est démarré dans XAMPP")
