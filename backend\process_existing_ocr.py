"""
Script pour traiter l'OCR des documents existants
"""

import os
from sqlalchemy.orm import Session
from app.core.database import <PERSON>Local
from app.models.document import Document
from app.services.ocr_service import OCRService

def process_existing_documents():
    """Traite l'OCR pour tous les documents existants qui n'en ont pas"""
    print("🔍 Traitement OCR des documents existants...")
    
    db = SessionLocal()
    ocr_service = OCRService()
    
    try:
        # Récupérer tous les documents sans OCR
        documents = db.query(Document).filter(
            (Document.ocr_text.is_(None)) | (Document.ocr_text == "")
        ).all()
        
        print(f"📄 {len(documents)} document(s) à traiter")
        
        if not documents:
            print("✅ Tous les documents ont déjà été traités")
            return
        
        for i, document in enumerate(documents, 1):
            print(f"\n📄 Document {i}/{len(documents)}: {document.title}")
            print(f"   Fichier: {document.original_filename}")
            print(f"   Type: {document.mime_type}")
            print(f"   Chemin: {document.file_path}")
            
            # Vérifier que le fichier existe
            if not os.path.exists(document.file_path):
                print(f"   ❌ Fichier non trouvé: {document.file_path}")
                continue
            
            try:
                # Traiter l'OCR selon le type de fichier
                if document.mime_type == "application/pdf":
                    print("   🔍 Traitement OCR PDF...")
                    ocr_result = ocr_service.extract_text_from_pdf(document.file_path)
                elif document.mime_type.startswith("image/"):
                    print("   🔍 Traitement OCR Image...")
                    ocr_result = ocr_service.extract_text_from_image(document.file_path)
                else:
                    print(f"   ⚠️ Type de fichier non supporté pour OCR: {document.mime_type}")
                    continue
                
                if ocr_result and ocr_result.get("text"):
                    # Mettre à jour le document avec le texte OCR
                    document.ocr_text = ocr_result["text"]
                    document.ocr_confidence = ocr_result.get("confidence", 0)
                    
                    db.commit()
                    
                    text_length = len(ocr_result["text"])
                    confidence = ocr_result.get("confidence", 0)
                    
                    print(f"   ✅ OCR réussi!")
                    print(f"      Texte extrait: {text_length} caractères")
                    print(f"      Confiance: {confidence}%")
                    
                    # Afficher un aperçu du texte
                    preview = ocr_result["text"][:200].replace('\n', ' ')
                    print(f"      Aperçu: {preview}...")
                    
                else:
                    print(f"   ❌ Aucun texte extrait")
                    
            except Exception as e:
                print(f"   ❌ Erreur OCR: {e}")
                continue
        
        print(f"\n🎉 Traitement terminé!")
        
        # Statistiques finales
        total_docs = db.query(Document).count()
        docs_with_ocr = db.query(Document).filter(
            Document.ocr_text.isnot(None),
            Document.ocr_text != ""
        ).count()
        
        print(f"\n📊 Statistiques:")
        print(f"   Total documents: {total_docs}")
        print(f"   Avec OCR: {docs_with_ocr}")
        print(f"   Sans OCR: {total_docs - docs_with_ocr}")
        
    except Exception as e:
        print(f"❌ Erreur générale: {e}")
        db.rollback()
    finally:
        db.close()

def test_ocr_after_processing():
    """Test l'OCR après traitement"""
    print("\n🔍 Vérification après traitement...")
    
    db = SessionLocal()
    
    try:
        documents = db.query(Document).all()
        
        for doc in documents:
            has_ocr = bool(doc.ocr_text and doc.ocr_text.strip())
            status = "✅" if has_ocr else "❌"
            
            print(f"{status} {doc.title}")
            if has_ocr:
                print(f"   📝 {len(doc.ocr_text)} caractères, confiance: {doc.ocr_confidence}%")
                # Aperçu du contenu
                preview = doc.ocr_text[:100].replace('\n', ' ')
                print(f"   👁️ Aperçu: {preview}...")
            else:
                print(f"   ❌ Pas de contenu OCR")
            print()
            
    except Exception as e:
        print(f"❌ Erreur: {e}")
    finally:
        db.close()

if __name__ == "__main__":
    print("🚀 Traitement OCR des documents existants")
    print("=" * 60)
    
    process_existing_documents()
    test_ocr_after_processing()
    
    print("\n💡 Maintenant vous pouvez:")
    print("   • Rechercher dans le contenu des documents")
    print("   • Voir le texte extrait de chaque document")
    print("   • Utiliser la recherche avancée")
    print("\n🌐 Testez dans l'interface web:")
    print("   http://localhost:5173")
