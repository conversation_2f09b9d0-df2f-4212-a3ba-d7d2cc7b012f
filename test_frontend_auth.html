<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Authentification Frontend</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 600px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .log { background-color: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 Test Authentification Frontend</h1>
        
        <div class="test-section info">
            <h3>Instructions</h3>
            <p>Ce test simule exactement ce que fait le frontend React.</p>
            <p><strong>Identifiants:</strong> admin / admin123</p>
        </div>

        <div class="test-section">
            <h3>Test de Connexion</h3>
            <button onclick="testLogin()">🔐 Tester la Connexion</button>
            <button onclick="testProfile()">👤 Tester le Profil</button>
            <button onclick="clearStorage()">🗑️ Vider le Storage</button>
        </div>

        <div class="test-section">
            <h3>État Actuel</h3>
            <div id="status"></div>
        </div>

        <div class="test-section">
            <h3>Logs</h3>
            <div id="logs" class="log"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8000';
        
        function log(message) {
            const logs = document.getElementById('logs');
            const timestamp = new Date().toLocaleTimeString();
            logs.textContent += `[${timestamp}] ${message}\n`;
            logs.scrollTop = logs.scrollHeight;
            console.log(message);
        }

        function updateStatus() {
            const token = localStorage.getItem('token');
            const user = localStorage.getItem('user');
            const status = document.getElementById('status');
            
            if (token && user) {
                const userData = JSON.parse(user);
                status.innerHTML = `
                    <div class="success">
                        ✅ <strong>Connecté</strong><br>
                        Utilisateur: ${userData.username}<br>
                        Email: ${userData.email}<br>
                        Token: ${token.substring(0, 30)}...
                    </div>
                `;
            } else {
                status.innerHTML = `
                    <div class="error">
                        ❌ <strong>Non connecté</strong><br>
                        Pas de token ou d'utilisateur en localStorage
                    </div>
                `;
            }
        }

        async function testLogin() {
            log('🔐 Début du test de connexion...');
            
            try {
                // Étape 1: Login
                log('📤 Envoi de la requête de login...');
                const loginResponse = await fetch(`${API_BASE}/api/auth/auth`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123'
                    })
                });

                log(`📥 Réponse login: ${loginResponse.status}`);
                
                if (!loginResponse.ok) {
                    const errorText = await loginResponse.text();
                    throw new Error(`Login failed: ${loginResponse.status} - ${errorText}`);
                }

                const tokenData = await loginResponse.json();
                log(`✅ Token reçu: ${tokenData.access_token.substring(0, 30)}...`);

                // Étape 2: Sauvegarder le token
                localStorage.setItem('token', tokenData.access_token);
                log('💾 Token sauvegardé dans localStorage');

                // Étape 3: Récupérer le profil
                log('📤 Récupération du profil...');
                const profileResponse = await fetch(`${API_BASE}/api/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${tokenData.access_token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`📥 Réponse profil: ${profileResponse.status}`);

                if (!profileResponse.ok) {
                    const errorText = await profileResponse.text();
                    throw new Error(`Profile failed: ${profileResponse.status} - ${errorText}`);
                }

                const user = await profileResponse.json();
                log(`✅ Profil récupéré: ${user.username} (${user.email})`);

                // Étape 4: Sauvegarder l'utilisateur
                localStorage.setItem('user', JSON.stringify(user));
                log('💾 Utilisateur sauvegardé dans localStorage');

                log('🎉 CONNEXION RÉUSSIE !');
                updateStatus();

            } catch (error) {
                log(`❌ ERREUR: ${error.message}`);
                updateStatus();
            }
        }

        async function testProfile() {
            log('👤 Test de récupération du profil...');
            
            const token = localStorage.getItem('token');
            if (!token) {
                log('❌ Pas de token disponible');
                return;
            }

            try {
                const response = await fetch(`${API_BASE}/api/auth/me`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                log(`📥 Réponse: ${response.status}`);

                if (response.ok) {
                    const user = await response.json();
                    log(`✅ Profil: ${user.username} (${user.email})`);
                } else {
                    const errorText = await response.text();
                    log(`❌ Erreur: ${response.status} - ${errorText}`);
                }
            } catch (error) {
                log(`❌ ERREUR: ${error.message}`);
            }
        }

        function clearStorage() {
            localStorage.removeItem('token');
            localStorage.removeItem('user');
            log('🗑️ localStorage vidé');
            updateStatus();
        }

        // Initialisation
        updateStatus();
        log('🚀 Test d\'authentification frontend initialisé');
    </script>
</body>
</html>
