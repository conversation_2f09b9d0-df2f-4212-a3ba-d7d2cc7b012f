"""
Configuration de la base de données SQLAlchemy
"""

from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
from .config import settings

# Créer le moteur de base de données
if settings.DATABASE_URL.startswith("sqlite"):
    # Configuration spéciale pour SQLite
    engine = create_engine(
        settings.database_url_sync,
        echo=settings.DEBUG,
        connect_args={"check_same_thread": False}  # Nécessaire pour SQLite avec FastAPI
    )
else:
    # Configuration pour MySQL/PostgreSQL
    engine = create_engine(
        settings.database_url_sync,
        echo=settings.DEBUG,
        pool_pre_ping=True,
        pool_recycle=300,
        pool_size=10,
        max_overflow=20
    )

# Créer la session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Base pour les modèles
Base = declarative_base()

def get_db():
    """
    Générateur de session de base de données pour l'injection de dépendance FastAPI
    """
    db = SessionLocal()
    try:
        yield db
    except Exception as e:
        print(f"Erreur de session DB: {e}")
        db.rollback()
        raise
    finally:
        db.close()
